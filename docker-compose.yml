services:
  redis-db:
    image: redis:alpine
    command: redis-server --requirepass pass
    ports:
      - 6379:6379
    container_name: redis-db
    networks:
      - app-network

  mycelium:
    build: 
      context: ./mycelium-peer
      dockerfile: Dockerfile
    container_name: mycelium
    cap_add: # Required for TUN device creation by mycelium process
      - NET_ADMIN
      - SYS_ADMIN
    devices:
      - /dev/net/tun:/dev/net/tun
    ports:
      - "8080:8080" # exposes the backend API 
    networks:
      - app-network # connect to redis with network
    volumes:
      - mycelium-data:/var/lib/mycelium
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    depends_on:
      - redis-db
      - mycelium
    working_dir: /opt/project
    volumes:
      - ./backend/kubecloud.db:/app/kubecloud.db
      - ./backend/config.json:/app/config.json
      - /root/.ssh:/root/.ssh:ro
    environment:
      - MYCELIUM_HOST=localhost
      - REDIS_HOST=redis-db
      - REDIS_PORT=6379
    command: ["/kubecloud", "--config", "/app/config.json"]
    network_mode: "service:mycelium" # inherits mycelium's network, including app-network access

  frontend:
    environment:
      - VITE_API_BASE_URL=http://localhost:8080/api
      - VITE_STRIPE_PUBLISHABLE_KEY=""
    build:
      context: frontend/kubecloud/.
      dockerfile: Dockerfile
    ports:
       - "8000:80"

networks:
  app-network:
    driver: bridge

volumes:
  mycelium-data: 

