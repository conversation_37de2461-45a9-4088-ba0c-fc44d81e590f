{"swagger": "2.0", "info": {"description": "API documentation for KubeCloud.", "title": "KubeCloud API", "contact": {}, "version": "1.0"}, "basePath": "/api/v1", "paths": {"/invoices": {"get": {"security": [{"AdminMiddleware": []}], "description": "Returns a list of all invoices", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Get all invoices", "operationId": "get-all-invoices", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Invoice"}}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/nodes": {"get": {"description": "Retrieves a list of nodes from the grid proxy based on the provided filters.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["nodes"], "summary": "List nodes", "operationId": "list-nodes", "parameters": [{"type": "boolean", "description": "Filter by healthy nodes (default: true)", "name": "healthy", "in": "query"}, {"type": "boolean", "description": "Filter by rentable nodes (default: true)", "name": "rentable", "in": "query"}, {"type": "integer", "description": "Limit the number of nodes returned (default: 50)", "name": "limit", "in": "query"}, {"type": "integer", "description": "Offset for pagination (default: 0)", "name": "offset", "in": "query"}], "responses": {"200": {"description": "Nodes are retrieved successfully", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "400": {"description": "Invalid filter parameters", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/pending-records": {"get": {"security": [{"AdminMiddleware": []}], "description": "Returns all pending records in the system", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "List pending records", "operationId": "list-pending-records", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/app.PendingRecordsResponse"}}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/system/maintenance/status": {"get": {"security": [{"AdminMiddleware": []}], "description": "Gets maintenance mode for the system", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Get maintenance mode", "operationId": "get-maintenance-mode", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}, "put": {"security": [{"AdminMiddleware": []}], "description": "Sets maintenance mode for the system", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Set maintenance mode", "operationId": "set-maintenance-mode", "parameters": [{"description": "Maintenance Mode Status", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.MaintenanceModeStatus"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user": {"get": {"description": "Retrieves all data of the user", "produces": ["application/json"], "tags": ["users"], "summary": "Get user details", "operationId": "get-user", "responses": {"200": {"description": "User is retrieved successfully", "schema": {"$ref": "#/definitions/app.GetUserResponse"}}, "404": {"description": "User is not found", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/balance": {"get": {"description": "Retrieves the user's balance in USD", "produces": ["application/json"], "tags": ["users"], "summary": "Get user balance", "operationId": "get-user-balance", "responses": {"200": {"description": "Balance fetched successfully", "schema": {"$ref": "#/definitions/app.UserBalanceResponse"}}, "404": {"description": "User is not found", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/balance/charge": {"post": {"description": "Charges the user's balance using a payment method", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Charge user balance", "operationId": "charge-balance", "parameters": [{"description": "Charge Balance Input", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.ChargeBalanceInput"}}], "responses": {"202": {"description": "workflow_id: string, email: string", "schema": {"$ref": "#/definitions/app.ChargeBalanceResponse"}}, "400": {"description": "Invalid request format or amount", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "404": {"description": "User is not found", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/change_password": {"put": {"description": "Changes the user's password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Change password", "operationId": "change-password", "parameters": [{"description": "Change Password Input", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.ChangePasswordInput"}}], "responses": {"202": {"description": "Password updated successfully", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "400": {"description": "Invalid request format or password mismatch", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "404": {"description": "User is not found", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/forgot_password": {"post": {"description": "Sends a verification code to the user's email for password reset", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Forgot password", "operationId": "forgot-password", "parameters": [{"description": "Email Input", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.EmailInput"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/app.RegisterResponse"}}, "400": {"description": "Invalid request format", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "404": {"description": "User is not found", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/forgot_password/verify": {"post": {"description": "Verifies the code sent to the user's email for password reset", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Verify forgot password code", "operationId": "verify-forgot-password-code", "parameters": [{"description": "Verify Code Input", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.VerifyCodeInput"}}], "responses": {"201": {"description": "Verification successful", "schema": {"$ref": "#/definitions/internal.TokenPair"}}, "400": {"description": "Invalid request format or verification failed", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/invoice": {"get": {"security": [{"UserMiddleware": []}], "description": "Returns a list of invoices for a user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["invoices"], "summary": "Get invoices", "operationId": "get-invoices", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Invoice"}}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/invoice/{invoice_id}": {"get": {"security": [{"UserMiddleware": []}], "description": "Downloads an invoice by ID", "consumes": ["application/json"], "produces": ["application/octet-stream"], "tags": ["invoices"], "summary": "Download invoice", "operationId": "download-invoice", "parameters": [{"type": "string", "description": "Invoice ID", "name": "invoice_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "file"}}, "404": {"description": "Invoice is not found", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/login": {"post": {"description": "Logs a user in. Checks KYC verification status and updates user sponsorship status if needed. Login is not blocked by KYC errors.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Login user (KYC verification checked)", "operationId": "login-user", "parameters": [{"description": "Login Input", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.LoginInput"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/internal.TokenPair"}}, "400": {"description": "Invalid request format", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "401": {"description": "<PERSON><PERSON> failed", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/nodes": {"get": {"security": [{"UserMiddleware": []}], "description": "Returns a list of reserved nodes for a user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["nodes"], "summary": "List reserved nodes", "operationId": "list-reserved-nodes", "responses": {"201": {"description": "Created", "schema": {"type": "array", "items": {"$ref": "#/definitions/app.APIResponse"}}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/nodes/unreserve/{contract_id}": {"delete": {"security": [{"UserMiddleware": []}], "description": "Unreserve a node for a user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["nodes"], "summary": "Unreserve node", "operationId": "unreserve-node", "parameters": [{"type": "string", "description": "Contract ID", "name": "contract_id", "in": "path", "required": true}], "responses": {"202": {"description": "Accepted", "schema": {"$ref": "#/definitions/app.UnreserveNodeResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "404": {"description": "User is not found", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/nodes/{node_id}": {"post": {"security": [{"UserMiddleware": []}], "description": "Reserves a node for a user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["nodes"], "summary": "Reserve node", "operationId": "reserve-node", "parameters": [{"type": "string", "description": "Node ID", "name": "node_id", "in": "path", "required": true}], "responses": {"202": {"description": "Accepted", "schema": {"$ref": "#/definitions/app.ReserveNodeResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "404": {"description": "No nodes are available for rent.", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/pending-records": {"get": {"security": [{"BearerAuth": []}], "description": "Returns user pending records in the system", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "List user pending records", "operationId": "list-user-pending-records", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/app.PendingRecordsResponse"}}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/redeem/{voucher_code}": {"put": {"description": "Redeems a voucher for the user", "produces": ["application/json"], "tags": ["users"], "summary": "Redeem voucher", "operationId": "redeem-voucher", "parameters": [{"type": "string", "description": "Voucher Code", "name": "voucher_code", "in": "path", "required": true}], "responses": {"202": {"description": "workflow_id: string, voucher_code: string, amount: float64, email: string", "schema": {"$ref": "#/definitions/app.RedeemVoucherResponse"}}, "400": {"description": "Invalid voucher code, already redeemed, or expired", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "404": {"description": "User or voucher are not found", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/refresh": {"post": {"description": "Refreshes the access token using a valid refresh token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Refresh access token", "operationId": "refresh-token", "parameters": [{"description": "Refresh Token Input", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.RefreshTokenInput"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/app.RefreshTokenResponse"}}, "400": {"description": "Invalid request format", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "401": {"description": "Invalid or expired refresh token", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/register": {"post": {"description": "Registers a new user, sets up blockchain account, and creates KYC sponsorship. Sends verification code to email.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Register user (with KYC sponsorship)", "operationId": "register-user", "parameters": [{"description": "Register Input", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.RegisterInput"}}], "responses": {"201": {"description": "workflow_id: string, email: string", "schema": {"$ref": "#/definitions/app.RegisterUserResponse"}}, "400": {"description": "Invalid request format", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "409": {"description": "User already registered", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/register/verify": {"post": {"description": "Verifies the email using the registration code", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Verify registration code", "operationId": "verify-register-code", "parameters": [{"description": "Verify Code Input", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.VerifyCodeInput"}}], "responses": {"202": {"description": "workflow_id: string, email: string", "schema": {"$ref": "#/definitions/app.RegisterUserResponse"}}, "400": {"description": "Invalid request format or verification failed", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/ssh-keys": {"get": {"security": [{"BearerAuth": []}], "description": "Lists all SSH keys for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "List user SSH keys", "operationId": "list-ssh-keys", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.SSHKey"}}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Adds a new SSH key for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Add SSH key", "operationId": "add-ssh-key", "parameters": [{"description": "SSH Key Input", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.SSHKeyInput"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.SSHKey"}}, "400": {"description": "Invalid request format", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/user/ssh-keys/{ssh_key_id}": {"delete": {"security": [{"BearerAuth": []}], "description": "Deletes an SSH key for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Delete SSH key", "operationId": "delete-ssh-key", "parameters": [{"type": "integer", "description": "SSH Key ID", "name": "ssh_key_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "400": {"description": "Invalid SSH key ID", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "404": {"description": "SSH key not found", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/users": {"get": {"security": [{"AdminMiddleware": []}], "description": "Returns a list of all users", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Get all users", "operationId": "get-all-users", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.User"}}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/users/mail": {"post": {"security": [{"AdminMiddleware": []}], "description": "Allows admin to send a custom email to all users with optional file attachments. Returns detailed statistics about successful and failed email deliveries.", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["admin"], "summary": "Send mail to all users", "operationId": "admin-mail-all-users", "parameters": [{"type": "string", "description": "Email subject", "name": "subject", "in": "formData", "required": true}, {"type": "string", "description": "Email body content", "name": "body", "in": "formData", "required": true}, {"type": "file", "description": "Email attachments (multiple files allowed)", "name": "attachments", "in": "formData"}], "responses": {"200": {"description": "Email sending results with delivery statistics", "schema": {"allOf": [{"$ref": "#/definitions/app.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/app.SendMailResponse"}}}]}}, "400": {"description": "Invalid request format", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/users/{user_id}": {"delete": {"security": [{"AdminMiddleware": []}], "description": "Deletes a user from the system", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Delete a user", "operationId": "delete-user", "parameters": [{"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "400": {"description": "Invalid user ID", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "403": {"description": "Admins cannot delete their own account", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/users/{user_id}/credit": {"post": {"security": [{"AdminMiddleware": []}], "description": "Credits a specific user's balance", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Credit user balance", "operationId": "credit-user", "parameters": [{"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}, {"description": "Credit Request Input", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.CreditRequestInput"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/app.CreditUserResponse"}}, "400": {"description": "Invalid request format or user ID", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/vouchers": {"get": {"security": [{"AdminMiddleware": []}], "description": "Returns all vouchers in the system", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "List vouchers", "operationId": "list-vouchers", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Voucher"}}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/vouchers/generate": {"post": {"security": [{"AdminMiddleware": []}], "description": "Generates a bulk of vouchers", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Generate vouchers", "operationId": "generate-vouchers", "parameters": [{"description": "Generate Vouchers Input", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/app.GenerateVouchersInput"}}], "responses": {"201": {"description": "Created", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Voucher"}}}, "400": {"description": "Invalid request format", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}, "/workflow/{workflow_id}": {"get": {"description": "Returns the status of a workflow by its ID.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["workflow"], "summary": "Get workflow status", "operationId": "get-workflow-status", "parameters": [{"type": "string", "description": "Workflow ID", "name": "workflow_id", "in": "path", "required": true}], "responses": {"200": {"description": "Workflow status returned successfully", "schema": {"type": "string"}}, "400": {"description": "Invalid request or missing workflow ID", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "404": {"description": "Workflow not found", "schema": {"$ref": "#/definitions/app.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/app.APIResponse"}}}}}}, "definitions": {"app.APIResponse": {"type": "object", "properties": {"data": {}, "error": {"type": "string"}, "message": {"type": "string"}, "status": {"type": "integer"}}}, "app.ChangePasswordInput": {"type": "object", "required": ["confirm_password", "email", "password"], "properties": {"confirm_password": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string", "maxLength": 64, "minLength": 8}}}, "app.ChargeBalanceInput": {"type": "object", "required": ["amount", "card_type", "payment_method_id"], "properties": {"amount": {"type": "number"}, "card_type": {"type": "string"}, "payment_method_id": {"type": "string"}}}, "app.ChargeBalanceResponse": {"type": "object", "properties": {"email": {"type": "string"}, "workflow_id": {"type": "string"}}}, "app.CreditRequestInput": {"type": "object", "required": ["amount", "memo"], "properties": {"amount": {"type": "number"}, "memo": {"type": "string", "maxLength": 255, "minLength": 3}}}, "app.CreditUserResponse": {"type": "object", "properties": {"amount": {"type": "number"}, "memo": {"type": "string"}, "user": {"type": "string"}}}, "app.EmailInput": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string"}}}, "app.GenerateVouchersInput": {"type": "object", "required": ["count", "expire_after_days", "value"], "properties": {"count": {"type": "integer"}, "expire_after_days": {"type": "integer"}, "value": {"type": "number"}}}, "app.GetUserResponse": {"type": "object", "required": ["email", "password", "username"], "properties": {"account_address": {"type": "string"}, "admin": {"type": "boolean"}, "code": {"type": "integer"}, "credit_card_balance": {"description": "millicent, money from credit card", "type": "integer"}, "credited_balance": {"description": "millicent, manually added by admin or from vouchers", "type": "integer"}, "debt": {"description": "millicent", "type": "integer"}, "email": {"type": "string"}, "id": {"type": "integer"}, "password": {"type": "array", "items": {"type": "integer"}}, "pending_balance_usd": {"type": "number"}, "sponsored": {"type": "boolean"}, "ssh_key": {"type": "string"}, "stripe_customer_id": {"type": "string"}, "updated_at": {"type": "string"}, "username": {"type": "string"}, "verified": {"type": "boolean"}}}, "app.LoginInput": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string"}, "password": {"type": "string", "maxLength": 64, "minLength": 3}}}, "app.MaintenanceModeStatus": {"type": "object", "properties": {"enabled": {"type": "boolean"}}}, "app.PendingRecordsResponse": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "tft_amount": {"description": "TFTs are multiplied by 1e7", "type": "integer"}, "transferred_tft_amount": {"type": "integer"}, "transferred_usd_amount": {"type": "number"}, "updated_at": {"type": "string"}, "usd_amount": {"type": "number"}, "user_id": {"type": "integer"}}}, "app.RedeemVoucherResponse": {"type": "object", "properties": {"amount": {"type": "number"}, "email": {"type": "string"}, "voucher_code": {"type": "string"}, "workflow_id": {"type": "string"}}}, "app.RefreshTokenInput": {"type": "object", "required": ["refresh_token"], "properties": {"refresh_token": {"type": "string"}}}, "app.RefreshTokenResponse": {"type": "object", "properties": {"access_token": {"type": "string"}}}, "app.RegisterInput": {"type": "object", "required": ["confirm_password", "email", "name", "password"], "properties": {"confirm_password": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string", "maxLength": 64, "minLength": 3}, "password": {"type": "string", "maxLength": 64, "minLength": 8}}}, "app.RegisterResponse": {"type": "object", "properties": {"email": {"type": "string"}, "timeout": {"type": "string"}}}, "app.RegisterUserResponse": {"type": "object", "properties": {"email": {"type": "string"}, "workflow_id": {"type": "string"}}}, "app.ReserveNodeResponse": {"type": "object", "properties": {"email": {"type": "string"}, "node_id": {"type": "integer"}, "workflow_id": {"type": "string"}}}, "app.SSHKeyInput": {"type": "object", "required": ["name", "public_key"], "properties": {"name": {"type": "string"}, "public_key": {"type": "string"}}}, "app.SendMailResponse": {"type": "object", "properties": {"failed_emails": {"type": "array", "items": {"type": "string"}}, "failed_emails_count": {"type": "integer"}, "successful_emails": {"type": "integer"}, "total_users": {"type": "integer"}}}, "app.UnreserveNodeResponse": {"type": "object", "properties": {"contract_id": {"type": "integer"}, "email": {"type": "string"}, "workflow_id": {"type": "string"}}}, "app.UserBalanceResponse": {"type": "object", "properties": {"balance_usd": {"type": "number"}, "debt_usd": {"type": "number"}, "pending_balance_usd": {"type": "number"}}}, "app.VerifyCodeInput": {"type": "object", "required": ["code", "email"], "properties": {"code": {"type": "integer"}, "email": {"type": "string"}}}, "internal.TokenPair": {"type": "object", "properties": {"access_token": {"type": "string"}, "refresh_token": {"type": "string"}}}, "models.Invoice": {"type": "object", "required": ["user_id"], "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "nodes": {"type": "array", "items": {"$ref": "#/definitions/models.NodeItem"}}, "tax": {"description": "TODO:", "type": "number"}, "total": {"type": "number"}, "user_id": {"type": "integer"}}}, "models.NodeItem": {"type": "object", "properties": {"contract_id": {"type": "integer"}, "cost": {"type": "number"}, "id": {"type": "integer"}, "invoice_id": {"type": "integer"}, "node_id": {"type": "integer"}, "period": {"type": "number"}, "rent_created_at": {"type": "string"}}}, "models.SSHKey": {"type": "object", "required": ["name", "public_key", "userID"], "properties": {"created_at": {"type": "string"}, "id": {"description": "Primary key", "type": "integer"}, "name": {"description": "Unique name per user", "type": "string"}, "public_key": {"description": "Unique public key per user", "type": "string"}, "updated_at": {"type": "string"}, "userID": {"description": "User owner", "type": "integer"}}}, "models.User": {"type": "object", "required": ["email", "password", "username"], "properties": {"account_address": {"type": "string"}, "admin": {"type": "boolean"}, "code": {"type": "integer"}, "credit_card_balance": {"description": "millicent, money from credit card", "type": "integer"}, "credited_balance": {"description": "millicent, manually added by admin or from vouchers", "type": "integer"}, "debt": {"description": "millicent", "type": "integer"}, "email": {"type": "string"}, "id": {"type": "integer"}, "password": {"type": "array", "items": {"type": "integer"}}, "sponsored": {"type": "boolean"}, "ssh_key": {"type": "string"}, "stripe_customer_id": {"type": "string"}, "updated_at": {"type": "string"}, "username": {"type": "string"}, "verified": {"type": "boolean"}}}, "models.Voucher": {"type": "object", "required": ["code", "created_at", "expires_at", "value"], "properties": {"code": {"type": "string"}, "created_at": {"type": "string"}, "expires_at": {"type": "string"}, "id": {"type": "integer"}, "redeemed": {"type": "boolean"}, "value": {"type": "number"}}}}}